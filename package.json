{"name": "atropos-pos-sys", "version": "1.0.0", "description": "Atropos Restaurant POS System", "type": "module", "private": true, "workspaces": ["frontend", "backend"], "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "npm run dev --workspace=backend", "dev:frontend": "npm run dev --workspace=frontend", "build": "npm run build --workspace=backend && npm run build --workspace=frontend", "build:backend": "npm run build --workspace=backend", "build:frontend": "npm run build --workspace=frontend", "start": "npm run start --workspace=backend", "db:migrate": "npm run db:migrate --workspace=backend", "db:generate": "npm run db:generate --workspace=backend", "db:studio": "npm run db:studio --workspace=backend"}, "devDependencies": {"concurrently": "^8.2.2"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}