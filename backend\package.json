{"name": "@atropos/backend", "version": "1.0.0", "description": "Atropos POS Backend API", "type": "module", "main": "dist/index.js", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "db:migrate": "prisma migrate dev", "db:generate": "prisma generate", "db:studio": "prisma studio", "db:push": "prisma db push", "db:reset": "prisma migrate reset"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "dotenv": "^16.3.1", "@prisma/client": "^5.7.1", "zod": "^3.22.4"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/node": "^20.10.5", "typescript": "^5.3.3", "tsx": "^4.6.2", "prisma": "^5.7.1"}}