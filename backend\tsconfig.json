{"compilerOptions": {"target": "ES2022", "module": "ESNext", "moduleResolution": "Node", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "allowJs": true, "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": false, "declaration": true, "outDir": "./dist", "rootDir": "./src", "baseUrl": "./src", "paths": {"@/*": ["./*"], "@/types/*": ["./types/*"], "@/utils/*": ["./utils/*"], "@/routes/*": ["./routes/*"], "@/middleware/*": ["./middleware/*"], "@/services/*": ["./services/*"], "@/config/*": ["./config/*"]}, "types": ["node"], "lib": ["ES2022"]}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"], "ts-node": {"esm": true}}