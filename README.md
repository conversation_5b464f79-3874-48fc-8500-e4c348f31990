# Atropos POS System

Modern restoran POS sistemi - Electron + React + Express + PostgreSQL

## Stack

### Frontend
- **Electron** - Desktop uygulama
- **React 18** - UI framework
- **TypeScript** - Type safety
- **Radix UI** - Component library
- **Tailwind CSS v3** - Styling
- **Vite** - Build tool

### Backend
- **Express** - Web framework
- **TypeScript** - Type safety
- **Prisma** - ORM
- **PostgreSQL** - Database

## Geliştirme

### Gereksinimler
- Node.js 18+
- PostgreSQL 14+
- npm 9+

### <PERSON><PERSON>lum

```bash
# Dependencies yükle
npm install

# Database setup
npm run db:migrate

# Development server başlat
npm run dev
```

### Scripts

```bash
# Tüm servisleri başlat
npm run dev

# Sadece backend
npm run dev:backend

# Sadece frontend
npm run dev:frontend

# Build
npm run build

# Database migration
npm run db:migrate

# Prisma studio
npm run db:studio
```

## <PERSON><PERSON>

```
atropos-pos-sys/
├── frontend/          # Electron + React app
├── backend/           # Express API server
├── docs/             # Dokümantasyon
└── package.json      # Root workspace config
```
