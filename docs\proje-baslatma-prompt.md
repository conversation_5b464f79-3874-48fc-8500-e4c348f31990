Basit bir restoran POS projesi başlatıyorum. Adı: Atropos

Stack:
- Frontend: Electron + React 18 + TypeScript + Radix UI + Tailwind CSS v3
- Backend: Express + TypeScript + Prisma + PostgreSQL

İlk adım olarak:
1. Frontend ve backend klasörlerini ayır
2. Her ikisi için TypeScript config
3. Backend'de Express + Prisma setup
4. Frontend'de Electron + React setup
5. Basit bir health-check API ve frontend'de test
6. PostgreSQL veritabanı kurulumu
7. Prisma migration'ları yap
8. sana verilen prisma şemasını kullan. kurallara uy. es modul kullan, commonjs kullanma. - ES Modules (type: "module"), npm kullan

ES Modules kullan (CommonJS yok)
TypeScript strict mode
Absolute imports (@/ prefix)
Environment based config

Karma<PERSON><PERSON><PERSON> yapılar kullanma, minimal ve çalışan kod ver.